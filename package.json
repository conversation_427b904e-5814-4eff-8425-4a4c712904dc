{"name": "bookamat-mcp", "version": "1.0.0", "description": "Model Context Protocol server for Bookamat accounting software API", "main": "build/index.js", "type": "module", "bin": {"bookamat-mcp": "./build/index.js"}, "scripts": {"build": "tsc && chmod 755 build/index.js", "dev": "tsx src/index.ts", "start": "node build/index.js", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "files": ["build"], "repository": {"type": "git", "url": "git+https://github.com/julianhandl/bookamat-mcp.git"}, "keywords": ["mcp", "model-context-protocol", "<PERSON><PERSON><PERSON>", "accounting", "api"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/julianhandl/bookamat-mcp/issues"}, "homepage": "https://github.com/julianhandl/bookamat-mcp#readme", "dependencies": {"@modelcontextprotocol/sdk": "^1.13.2", "axios": "^1.6.0", "dotenv": "^17.2.0", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^24.0.8", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "tsx": "^4.0.0", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}}