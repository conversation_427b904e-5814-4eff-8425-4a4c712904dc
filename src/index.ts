#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import { BookamatApiClient } from "./api-client.js";
import { getConfig, validateConfig } from "./config.js";
import {
  BookamatApiError,
  CreateBankAccountSchema,
  UpdateBankAccountSchema,
  CreateCostAccountSchema,
  CreatePurchaseTaxAccountSchema,
  CreateCostCenterSchema,
  UpdateCostCenterSchema,
} from "./types.js";

// Initialize configuration and API client
let apiClient: BookamatApiClient;

try {
  const config = getConfig();
  validateConfig(config);
  apiClient = new BookamatApiClient(config);
} catch (error) {
  console.error("Configuration error:", error instanceof Error ? error.message : error);
  process.exit(1);
}

// Create server instance
const server = new McpServer({
  name: "bookamat",
  version: "1.0.0",
  capabilities: {
    resources: {},
    tools: {},
  },
});

// Bank Accounts Tools
server.registerTool(
  "list_bank_accounts",
  {
    title: "List Bank Accounts",
    description: "List all bank accounts (Zahlungsmittelkonten) from Bookamat",
    inputSchema: {
      page: z.number().optional(),
      ordering: z.string().optional(),
      has_bookings: z.boolean().optional(),
    },
  },
  async (args) => {
    try {
      const result = await apiClient.getBankAccounts(args);
      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `Error: ${error instanceof Error ? error.message : "Unknown error"}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.registerTool(
  "get_bank_account",
  {
    title: "Get Bank Account",
    description: "Get details of a specific bank account by ID",
    inputSchema: {
      id: z.number(),
    },
  },
  async ({ id }) => {
    try {
      const result = await apiClient.getBankAccount(id);
      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `Error: ${error instanceof Error ? error.message : "Unknown error"}`,
          },
        ],
        isError: true,
      };
    }
  }
);

server.registerTool(
  "create_bank_account",
  {
    title: "Create Bank Account",
    description: "Create a new bank account",
    inputSchema: CreateBankAccountSchema,
  },
  async (args) => {
    try {
      const result = await apiClient.createBankAccount(args);
      return {
        content: [
          {
            type: "text",
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `Error: ${error instanceof Error ? error.message : "Unknown error"}`,
          },
        ],
        isError: true,
      };
    }
  }
);