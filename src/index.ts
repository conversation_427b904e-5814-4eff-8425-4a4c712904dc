#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { BookamatApiClient } from "./api-client.js";
import { getConfig, validateConfig } from "./config.js";
import { registerAllTools } from "./tools/index.js";
import 'dotenv/config'

/**
 * Bookamat MCP Server
 * 
 * This server provides a Model Context Protocol (MCP) interface to the Bookamat accounting system.
 * It allows clients to interact with Bookamat's accounting features through a standardized API.
 */

// Initialize configuration and API client
let apiClient: BookamatApiClient;

try {
  const config = getConfig();
  validateConfig(config);
  apiClient = new BookamatApiClient(config);
} catch (error) {
  console.error("Configuration error:", error instanceof Error ? error.message : error);
  process.exit(1);
}

// Create server instance
const server = new McpServer({
  name: "bookamat",
  version: "1.0.0",
  capabilities: {
    resources: {},
    tools: {},
  },
});

// Register all tools from the tools directory
registerAllTools(server, apiClient);

// Start the server
const transport = new StdioServerTransport();
server.connect(transport);

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.error("Shutting down Bookamat MCP Server...");
  await server.close();
  process.exit(0);
});

// Start the application
console.error("Bookamat MCP Server started");