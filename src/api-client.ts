import axios, { AxiosInstance, AxiosResponse } from "axios";
import {
  BookamatConfig,
  BookamatApiError,
  ApiResponse,
  BankAccount,
  CostAccount,
  FullPurchaseTaxAccount,
  CostCenter,
} from "./types.js";

export class BookamatApiClient {
  private client: AxiosInstance;
  private config: BookamatConfig;

  constructor(config: BookamatConfig) {
    this.config = config;

    const baseURL = `${config.baseUrl}/api/${config.apiVersion}/${config.country}/${config.year}/`;

    this.client = axios.create({
      baseURL,
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      auth: {
        username: config.username,
        password: config.apiKey,
      },
    });

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response) {
          throw new BookamatApiError(
            `API Error: ${error.response.status} - ${error.response.statusText}`,
            error.response.status,
            error.response.data
          );
        } else if (error.request) {
          throw new BookamatApiError("Network Error: No response received");
        } else {
          throw new BookamatApiError(`Request Error: ${error.message}`);
        }
      }
    );
  }

  // Bank Accounts (Zahlungsmittelkonten)
  async getBankAccounts(params?: {
    page?: number;
    ordering?: string;
    has_bookings?: boolean;
  }): Promise<ApiResponse<BankAccount>> {
    const response = await this.client.get("preferences/bankaccounts/", {
      params,
    });
    return response.data;
  }

  async getBankAccount(id: number): Promise<BankAccount> {
    const response = await this.client.get(`preferences/bankaccounts/${id}/`);
    return response.data;
  }

  // Cost Accounts (Steuerkonten)
  async getCostAccounts(params?: {
    page?: number;
    costaccount?: number;
    group?: string;
    inventory?: boolean;
    active?: boolean;
    has_bookings?: boolean;
    ordering?: string;
  }): Promise<ApiResponse<CostAccount>> {
    const response = await this.client.get("preferences/costaccounts/", {
      params,
    });
    return response.data;
  }

  async getCostAccount(id: number): Promise<CostAccount> {
    const response = await this.client.get(`preferences/costaccounts/${id}/`);
    return response.data;
  }

  // Purchase Tax Accounts (Umsatzsteuerkonten)
  async getPurchaseTaxAccounts(params?: {
    page?: number;
    purchasetaxaccount?: number;
    group?: string;
    reverse_charge?: boolean;
    ic_report?: boolean;
    ic_delivery?: boolean;
    ic_service?: boolean;
    ioss_report?: boolean;
    eu_oss_report?: boolean;
    active?: boolean;
    has_bookings?: boolean;
    ordering?: string;
  }): Promise<ApiResponse<FullPurchaseTaxAccount>> {
    const response = await this.client.get("preferences/purchasetaxaccounts/", {
      params,
    });
    return response.data;
  }

  async getPurchaseTaxAccount(id: number): Promise<FullPurchaseTaxAccount> {
    const response = await this.client.get(
      `preferences/purchasetaxaccounts/${id}/`
    );
    return response.data;
  }

  // Cost Centers (Kostenstellen)
  async getCostCenters(params?: {
    page?: number;
    has_bookings?: boolean;
    ordering?: string;
  }): Promise<ApiResponse<CostCenter>> {
    const response = await this.client.get("preferences/costcentres/", {
      params,
    });
    return response.data;
  }

  async getCostCenter(id: number): Promise<CostCenter> {
    const response = await this.client.get(`preferences/costcentres/${id}/`);
    return response.data;
  }
}
