import { z } from "zod";
import { ToolDefinition, createToolHandler } from "./tool-utils.js";
import type { BookamatApiClient } from "../api-client.js";

export const listCostAccountsTool: ToolDefinition = {
  name: "list_cost_accounts",
  title: "List Cost Accounts (Steuerkonten)",
  description: `List all cost accounts (tax account categories) from Bookamat.

Cost accounts are predefined tax account groups that categorize bookings by expense types. They define which expenses are deductible and how they should be treated for tax purposes.

WHEN TO USE:
- Before creating expense bookings to understand available tax categories
- To see which cost accounts are activated for the company
- To understand tax deductibility rules for different expense types
- To check which purchase tax accounts are associated with each cost account

IMPORTANT FOR BOOKINGS:
- Every expense booking requires a cost account ID (costaccount field)
- Cost accounts determine tax deductibility (deductibility_tax_percent, deductibility_amount_percent)
- They are linked to purchase tax accounts for VAT handling
- Only activated cost accounts (active: true) can be used in bookings

KEY FIELDS:
- costaccount: ID of the predefined cost account template
- group: "1" for income accounts, "2" for expense accounts
- inventory: true for inventory-related accounts
- deductibility_*_percent: Tax deductibility percentages
- purchasetaxaccounts: Associated VAT accounts

FILTERS AVAILABLE:
- costaccount: Filter by predefined cost account ID
- group: Filter by account group (1=income, 2=expense)
- inventory: Filter inventory-related accounts
- active: Filter activated accounts only
- has_bookings: Filter accounts with existing bookings`,
  inputSchema: {
    page: z.number().optional().describe("Page number for pagination"),
    costaccount: z.number().optional().describe("Filter by predefined cost account ID"),
    group: z.string().optional().describe("Filter by group: 1=income, 2=expense"),
    inventory: z.boolean().optional().describe("Filter inventory-related accounts"),
    active: z.boolean().optional().describe("Filter activated accounts only"),
    has_bookings: z.boolean().optional().describe("Filter accounts with bookings"),
    ordering: z.string().optional().describe("Sort by: id, costaccount, name, group"),
  },
  handler: async (args, apiClient) => {
    const result = await apiClient.getCostAccounts(args);
    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(result, null, 2),
        },
      ],
    };
  },
};

export const getCostAccountTool: ToolDefinition = {
  name: "get_cost_account",
  title: "Get Cost Account Details",
  description: `Get detailed information about a specific cost account by ID.

Use this to get complete details about a cost account including:
- Tax deductibility rules and percentages
- Associated purchase tax accounts for VAT handling
- Income tax indices and descriptions
- Booking statistics and activity

WHEN TO USE:
- When you need detailed tax information for a specific cost account
- To verify deductibility rules before creating expense bookings
- To understand which purchase tax accounts are available for this cost account
- To check account activity and booking statistics`,
  inputSchema: {
    id: z.number().describe("The unique ID of the cost account"),
  },
  handler: async ({ id }, apiClient) => {
    const result = await apiClient.getCostAccount(id);
    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(result, null, 2),
        },
      ],
    };
  },
};

export function registerCostAccountTools(server: any, apiClient: BookamatApiClient) {
  server.registerTool(
    listCostAccountsTool.name,
    {
      title: listCostAccountsTool.title,
      description: listCostAccountsTool.description,
      inputSchema: listCostAccountsTool.inputSchema,
    },
    createToolHandler(apiClient, listCostAccountsTool)
  );

  server.registerTool(
    getCostAccountTool.name,
    {
      title: getCostAccountTool.title,
      description: getCostAccountTool.description,
      inputSchema: getCostAccountTool.inputSchema,
    },
    createToolHandler(apiClient, getCostAccountTool)
  );
}
