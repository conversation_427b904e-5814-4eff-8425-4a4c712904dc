import { z } from "zod";
import { ToolDefinition, createTool<PERSON>and<PERSON> } from "./tool-utils.js";
import type { BookamatApiClient } from "../api-client.js";

export const listBankAccountsTool: ToolDefinition = {
  name: "list_bank_accounts",
  title: "List Bank Accounts (Zahlungsmittelkonten)",
  description: `List all bank accounts (payment method accounts) from Bookamat.

Bank accounts are fundamental accounts for income and expenses - they represent accounts that receive deposits and from which expenses are paid (e.g., bank accounts, credit cards, cash accounts).

WHEN TO USE:
- Before creating any booking, you need to know which bank accounts are available
- To see account balances and booking statistics
- To understand the company's payment methods structure

IMPORTANT FOR BOOKINGS:
- Every booking requires a bank account ID (bankaccount field)
- The bank account represents where money comes from (expenses) or goes to (income)
- Check counter_booked_bookings to see account activity
- flag_balance indicates if account appears in balance reports

FILTERS AVAILABLE:
- flag_balance: Filter by balance list inclusion (true/false)
- has_bookings: Filter accounts that have bookings (true/false)
- ordering: Sort by 'id', 'name', or 'position'`,
  inputSchema: {
    page: z.number().optional().describe("Page number for pagination"),
    flag_balance: z.boolean().optional().describe("Filter by balance list inclusion"),
    has_bookings: z.boolean().optional().describe("Filter accounts with bookings"),
    ordering: z.string().optional().describe("Sort by: id, name, position"),
  },
  handler: async (args: { page?: number; ordering?: string; has_bookings?: boolean; flag_balance?: boolean }, apiClient) => {
    const result = await apiClient.getBankAccounts(args);
    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(result, null, 2),
        },
      ],
    };
  },
};

export const getBankAccountTool: ToolDefinition = {
  name: "get_bank_account",
  title: "Get Bank Account Details",
  description: `Get detailed information about a specific bank account by ID.

Use this to get complete details about a bank account including:
- Account name and position
- Opening balance and balance list flag
- Booking counters (booked, open, deleted, templates)

WHEN TO USE:
- When you need detailed information about a specific bank account
- To verify account details before using in bookings
- To check account statistics and balances`,
  inputSchema: {
    id: z.number().describe("The unique ID of the bank account"),
  },
  handler: async (args: { id: number }, apiClient) => {
    const { id } = args;
    const result = await apiClient.getBankAccount(id);
    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(result, null, 2),
        },
      ],
    };
  },
};

export function registerBankAccountTools(server: any, apiClient: BookamatApiClient) {
  server.registerTool(
    listBankAccountsTool.name,
    {
      title: listBankAccountsTool.title,
      description: listBankAccountsTool.description,
      inputSchema: listBankAccountsTool.inputSchema,
    },
    createToolHandler(apiClient, listBankAccountsTool)
  );

  server.registerTool(
    getBankAccountTool.name,
    {
      title: getBankAccountTool.title,
      description: getBankAccountTool.description,
      inputSchema: getBankAccountTool.inputSchema,
    },
    createToolHandler(apiClient, getBankAccountTool)
  );
}
