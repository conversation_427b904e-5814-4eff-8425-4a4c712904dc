import { z } from "zod";
import { ToolDefinition, createT<PERSON><PERSON>and<PERSON> } from "./tool-utils.js";
import type { BookamatApiClient } from "../api-client.js";

export const listPurchaseTaxAccountsTool: ToolDefinition = {
  name: "list_purchase_tax_accounts",
  title: "List Purchase Tax Accounts (Umsatzsteuerkonten)",
  description: `List all purchase tax accounts (VAT account categories) from Bookamat.

Purchase tax accounts are tax-relevant groups that define how VAT should be calculated and reported. They determine VAT rates, reverse charge rules, and reporting requirements.

WHEN TO USE:
- Before creating bookings to understand available VAT treatment options
- To see which VAT accounts are activated for the company
- To understand VAT rates and special rules (reverse charge, IC reporting)
- To check VAT reporting requirements for different transaction types

IMPORTANT FOR BOOKINGS:
- Bookings can specify a purchase tax account for VAT handling
- Purchase tax accounts define allowed VAT rates (tax_values array)
- They control reverse charge VAT treatment
- They determine EU/IC reporting requirements (ic_report, ic_delivery, ic_service)
- IOSS and EU OSS reporting flags for cross-border transactions

KEY FIELDS:
- purchasetaxaccount: ID of the predefined purchase tax account template
- group: "1" for income accounts, "2" for expense accounts
- reverse_charge: true for reverse charge VAT treatment
- tax_values: Array of allowed VAT rates (e.g., ["0", "10", "20"])
- ic_report/ic_delivery/ic_service: EU intra-community reporting flags
- ioss_report/eu_oss_report: Cross-border VAT reporting flags
- index_purchasetax: Tax form reference numbers

FILTERS AVAILABLE:
- purchasetaxaccount: Filter by predefined account ID
- group: Filter by account group (1=income, 2=expense)
- reverse_charge: Filter reverse charge accounts
- ic_report/ic_delivery/ic_service: Filter EU reporting accounts
- ioss_report/eu_oss_report: Filter cross-border reporting accounts
- active: Filter activated accounts only
- has_bookings: Filter accounts with existing bookings`,
  inputSchema: {
    page: z.number().optional().describe("Page number for pagination"),
    purchasetaxaccount: z.number().optional().describe("Filter by predefined account ID"),
    group: z.string().optional().describe("Filter by group: 1=income, 2=expense"),
    reverse_charge: z.boolean().optional().describe("Filter reverse charge accounts"),
    ic_report: z.boolean().optional().describe("Filter IC reporting accounts"),
    ic_delivery: z.boolean().optional().describe("Filter IC delivery accounts"),
    ic_service: z.boolean().optional().describe("Filter IC service accounts"),
    ioss_report: z.boolean().optional().describe("Filter IOSS reporting accounts"),
    eu_oss_report: z.boolean().optional().describe("Filter EU OSS reporting accounts"),
    active: z.boolean().optional().describe("Filter activated accounts only"),
    has_bookings: z.boolean().optional().describe("Filter accounts with bookings"),
    ordering: z.string().optional().describe("Sort by: id, purchasetaxaccount, name, group"),
  },
  handler: async (args, apiClient) => {
    const result = await apiClient.getPurchaseTaxAccounts(args);
    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(result, null, 2),
        },
      ],
    };
  },
};

export const getPurchaseTaxAccountTool: ToolDefinition = {
  name: "get_purchase_tax_account",
  title: "Get Purchase Tax Account Details",
  description: `Get detailed information about a specific purchase tax account by ID.

Use this to get complete details about a purchase tax account including:
- Allowed VAT rates and tax calculation rules
- Reverse charge and special VAT treatment flags
- EU/IC reporting requirements and flags
- Tax form reference numbers and descriptions
- Booking statistics and activity

WHEN TO USE:
- When you need detailed VAT information for a specific account
- To verify VAT rates and rules before creating bookings
- To understand reporting requirements for different transaction types
- To check which VAT rates are allowed for specific account types`,
  inputSchema: {
    id: z.number().describe("The unique ID of the purchase tax account"),
  },
  handler: async ({ id }, apiClient) => {
    const result = await apiClient.getPurchaseTaxAccount(id);
    return {
      content: [
        {
          type: "text",
          text: JSON.stringify(result, null, 2),
        },
      ],
    };
  },
};

export function registerPurchaseTaxAccountTools(server: any, apiClient: BookamatApiClient) {
  server.registerTool(
    listPurchaseTaxAccountsTool.name,
    {
      title: listPurchaseTaxAccountsTool.title,
      description: listPurchaseTaxAccountsTool.description,
      inputSchema: listPurchaseTaxAccountsTool.inputSchema,
    },
    createToolHandler(apiClient, listPurchaseTaxAccountsTool)
  );

  server.registerTool(
    getPurchaseTaxAccountTool.name,
    {
      title: getPurchaseTaxAccountTool.title,
      description: getPurchaseTaxAccountTool.description,
      inputSchema: getPurchaseTaxAccountTool.inputSchema,
    },
    createToolHandler(apiClient, getPurchaseTaxAccountTool)
  );
}
