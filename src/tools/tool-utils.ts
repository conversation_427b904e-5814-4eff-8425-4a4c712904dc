import { BookamatApiClient } from "../api-client.js";
import { z } from "zod";

export interface ToolDefinition {
  name: string;
  title: string;
  description: string;
  inputSchema: any;
  handler: (args: any, apiClient: BookamatApiClient) => Promise<{
    content: Array<{ type: string; text: string }>;
    isError?: boolean;
  }>;
}

export function createToolHandler(apiClient: BookamatApiClient, tool: ToolDefinition) {
  return async (args: any) => {
    try {
      return await tool.handler(args, apiClient);
    } catch (error) {
      return {
        content: [
          {
            type: "text",
            text: `Error in ${tool.name}: ${error instanceof Error ? error.message : "Unknown error"}`,
          },
        ],
        isError: true,
      };
    }
  };
}
