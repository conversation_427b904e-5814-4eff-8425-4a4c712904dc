import { registerBankAccountTools } from "./bank-accounts.js";
import { registerCostAccountTools } from "./cost-accounts.js";
import { registerPurchaseTaxAccountTools } from "./purchase-tax-accounts.js";
import { BookamatApiClient } from "../api-client.js";

export function registerAllTools(server: any, apiClient: BookamatApiClient) {
  registerBankAccountTools(server, apiClient);
  registerCostAccountTools(server, apiClient);
  registerPurchaseTaxAccountTools(server, apiClient);
  
  // Add more tool registrations here as they are moved to separate files
}
