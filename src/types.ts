import { z } from "zod";

// Base API response structure
export const ApiResponseSchema = z.object({
  count: z.number(),
  next: z.string().nullable(),
  previous: z.string().nullable(),
  results: z.array(z.unknown()),
});

export type ApiResponse<T> = {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
};

// Bank Account (Zahlungsmittelkonto) schemas
export const BankAccountSchema = z.object({
  id: z.number(),
  name: z.string(),
  position: z.number(),
  flag_balance: z.boolean(),
  opening_balance: z.string(),
  counter_booked_bookings: z.number(),
  counter_open_bookings: z.number(),
  counter_deleted_bookings: z.number(),
  counter_bookingtemplates: z.number(),
});

export const CreateBankAccountSchema = z.object({
  name: z.string().max(40),
  position: z.number().optional(),
  flag_balance: z.boolean().optional(),
  opening_balance: z.string().optional(),
});

export const UpdateBankAccountSchema = z.object({
  name: z.string().max(40).optional(),
  position: z.number().optional(),
  flag_balance: z.boolean().optional(),
  opening_balance: z.string().optional(),
});

export type BankAccount = z.infer<typeof BankAccountSchema>;
export type CreateBankAccount = z.infer<typeof CreateBankAccountSchema>;
export type UpdateBankAccount = z.infer<typeof UpdateBankAccountSchema>;

// Cost Account (Steuerkonto) schemas - updated based on API documentation
export const PurchaseTaxAccountRefSchema = z.object({
  id: z.number(),
  name: z.string(),
});

export const CostAccountSchema = z.object({
  id: z.number(),
  costaccount: z.number(), // ID of predefined cost account
  name: z.string(),
  section: z.string(),
  group: z.string(), // "1" for income, "2" for expense
  inventory: z.boolean(),
  index_incometax: z.array(z.string()),
  deductibility_tax_percent: z.string().nullable(),
  deductibility_amount_percent: z.string().nullable(),
  description: z.string(),
  active: z.boolean(),
  purchasetaxaccounts: z.array(PurchaseTaxAccountRefSchema),
  counter_booked_bookings: z.number(),
  counter_open_bookings: z.number(),
  counter_deleted_bookings: z.number(),
  counter_bookingtemplates: z.number(),
});

export const CreateCostAccountSchema = z.object({
  costaccount: z.number(), // ID of predefined cost account to activate
});

export type CostAccount = z.infer<typeof CostAccountSchema>;
export type CreateCostAccount = z.infer<typeof CreateCostAccountSchema>;
export type PurchaseTaxAccountRef = z.infer<typeof PurchaseTaxAccountRefSchema>;

// Purchase Tax Account (Umsatzsteuerkonto) full schemas - updated based on API documentation
export const PurchaseTaxAccountSchema = z.object({
  id: z.number(),
  purchasetaxaccount: z.number(), // ID of predefined purchase tax account
  name: z.string(),
  section: z.string(),
  group: z.string(), // "1" for income, "2" for expense
  reverse_charge: z.boolean(),
  ic_report: z.boolean(), // Zusammenfassende Meldung
  ic_delivery: z.boolean(), // Zusammenfassende Meldung (Lieferung)
  ic_service: z.boolean(), // Zusammenfassende Meldung (Leistung)
  ioss_report: z.boolean(),
  eu_oss_report: z.boolean(),
  tax_values: z.array(z.string()), // Allowed tax rates
  index_purchasetax: z.array(z.string()), // Tax form reference numbers
  description: z.string(),
  active: z.boolean(),
  counter_booked_bookings: z.number(),
  counter_open_bookings: z.number(),
  counter_deleted_bookings: z.number(),
  counter_bookingtemplates: z.number(),
});

export const CreatePurchaseTaxAccountSchema = z.object({
  purchasetaxaccount: z.number(), // ID of predefined purchase tax account to activate
});

export type PurchaseTaxAccount = z.infer<typeof PurchaseTaxAccountSchema>;
export type CreatePurchaseTaxAccount = z.infer<typeof CreatePurchaseTaxAccountSchema>;

// Booking schemas - comprehensive implementation based on API documentation
export const BookingAmountSchema = z.object({
  group: z.string().describe("1 for income, 2 for expense"),
  bankaccount: z.object({
    id: z.number(),
    name: z.string(),
  }),
  costaccount: z.object({
    id: z.number(),
    name: z.string(),
  }),
  purchasetaxaccount: z.object({
    id: z.number(),
    name: z.string(),
  }),
  amount: z.string().describe("Gross amount (Bruttobetrag)"),
  amount_after_tax: z.string().describe("Net amount (Nettobetrag)"),
  tax_percent: z.string().describe("VAT percentage"),
  tax_value: z.string().describe("VAT amount in currency"),
  deductibility_tax_percent: z.string().describe("Business VAT deductibility percentage"),
  deductibility_tax_value: z.string().describe("Business VAT deductibility amount"),
  deductibility_amount_percent: z.string().describe("Business net amount deductibility percentage"),
  deductibility_amount_value: z.string().describe("Business net amount deductibility amount"),
  foreign_business_base: z.object({
    id: z.number(),
    vatin: z.string(),
  }).nullable(),
  country_dep: z.string().describe("Departure country code"),
  country_rec: z.string().describe("Destination country code"),
});

export const BookingTagSchema = z.object({
  id: z.number().describe("Booking tag ID"),
  tag: z.number().describe("Tag ID"),
  name: z.string().describe("Tag name"),
});

export const BookingAttachmentSchema = z.object({
  id: z.number().describe("Attachment ID"),
  name: z.string().describe("File name"),
  size: z.number().describe("File size in bytes"),
});

export const BookingSchema = z.object({
  id: z.number(),
  status: z.string().describe("1=booked, 2=open, 3=deleted, 4=imported"),
  title: z.string().describe("Booking title (max 50 chars)"),
  document_number: z.string().describe("Document number (month-number format)"),
  date: z.string().nullable().describe("Booking date (YYYY-MM-DD)"),
  date_invoice: z.string().nullable().describe("Invoice date (YYYY-MM-DD)"),
  date_delivery: z.string().nullable().describe("Delivery date (YYYY-MM-DD)"),
  date_order: z.string().nullable().describe("Order date (YYYY-MM-DD)"),
  costcentre: z.object({
    id: z.number(),
    name: z.string(),
  }).nullable(),
  amounts: z.array(BookingAmountSchema),
  tags: z.array(BookingTagSchema),
  attachments: z.array(BookingAttachmentSchema),
  vatin: z.string().describe("VAT identification number"),
  country: z.string().describe("Country code"),
  description: z.string().describe("Booking description"),
  create_date: z.string().describe("Creation timestamp"),
  update_date: z.string().describe("Last update timestamp"),
});

// Create booking amount schema for API requests
export const CreateBookingAmountSchema = z.object({
  bankaccount: z.number().describe("Bank account ID (required)"),
  costaccount: z.number().describe("Cost account ID (required)"),
  purchasetaxaccount: z.number().describe("Purchase tax account ID (required)"),
  amount: z.string().describe("Gross amount as decimal string (required)"),
  tax_percent: z.string().describe("VAT percentage as decimal string (required)"),
  deductibility_tax_percent: z.string().describe("Business VAT deductibility % (required)"),
  deductibility_amount_percent: z.string().describe("Business net amount deductibility % (required)"),
  foreign_business_base: z.number().nullable().optional().describe("Foreign business base ID"),
  country_dep: z.string().optional().describe("Departure country code"),
  country_rec: z.string().optional().describe("Destination country code"),
});

// Create booking schema for API requests
export const CreateBookingSchema = z.object({
  title: z.string().max(50).describe("Booking title (required, max 50 chars)"),
  date: z.string().optional().describe("Booking date YYYY-MM-DD (optional, creates open booking if omitted)"),
  date_invoice: z.string().optional().describe("Invoice date YYYY-MM-DD"),
  date_delivery: z.string().optional().describe("Delivery date YYYY-MM-DD"),
  date_order: z.string().optional().describe("Order date YYYY-MM-DD"),
  costcentre: z.number().optional().describe("Cost center ID"),
  amounts: z.array(CreateBookingAmountSchema).min(1).describe("Amount entries (required, at least one)"),
  vatin: z.string().max(20).optional().describe("VAT identification number (max 20 chars)"),
  country: z.string().optional().describe("Country code"),
  description: z.string().max(500).optional().describe("Booking description (max 500 chars)"),
});

export type Booking = z.infer<typeof BookingSchema>;
export type BookingAmount = z.infer<typeof BookingAmountSchema>;
export type BookingTag = z.infer<typeof BookingTagSchema>;
export type BookingAttachment = z.infer<typeof BookingAttachmentSchema>;
export type CreateBooking = z.infer<typeof CreateBookingSchema>;
export type CreateBookingAmount = z.infer<typeof CreateBookingAmountSchema>;

// Cost Center (Kostenstelle) schemas
export const CostCenterSchema = z.object({
  id: z.number(),
  name: z.string(),
  position: z.number(),
  counter_booked_bookings: z.number(),
  counter_open_bookings: z.number(),
  counter_deleted_bookings: z.number(),
  counter_bookingtemplates: z.number(),
});

export const CreateCostCenterSchema = z.object({
  name: z.string().max(40),
  position: z.number().optional(),
});

export const UpdateCostCenterSchema = z.object({
  name: z.string().max(40).optional(),
  position: z.number().optional(),
});

export type CostCenter = z.infer<typeof CostCenterSchema>;
export type CreateCostCenter = z.infer<typeof CreateCostCenterSchema>;
export type UpdateCostCenter = z.infer<typeof UpdateCostCenterSchema>;

// Configuration
export interface BookamatConfig {
  baseUrl: string;
  apiVersion: string;
  country: string;
  year: string;
  username: string;
  apiKey: string;
}

// Error types
export class BookamatApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public response?: any
  ) {
    super(message);
    this.name = "BookamatApiError";
  }
}
