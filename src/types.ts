import { z } from "zod";

// Base API response structure
export const ApiResponseSchema = z.object({
  count: z.number(),
  next: z.string().nullable(),
  previous: z.string().nullable(),
  results: z.array(z.unknown()),
});

export type ApiResponse<T> = {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
};

// Bank Account (Zahlungsmittelkonto) schemas
export const BankAccountSchema = z.object({
  id: z.number(),
  name: z.string(),
  position: z.number(),
  flag_balance: z.boolean(),
  opening_balance: z.string(),
  counter_booked_bookings: z.number(),
  counter_open_bookings: z.number(),
  counter_deleted_bookings: z.number(),
  counter_bookingtemplates: z.number(),
});

export const CreateBankAccountSchema = z.object({
  name: z.string().max(40),
  position: z.number().optional(),
  flag_balance: z.boolean().optional(),
  opening_balance: z.string().optional(),
});

export const UpdateBankAccountSchema = z.object({
  name: z.string().max(40).optional(),
  position: z.number().optional(),
  flag_balance: z.boolean().optional(),
  opening_balance: z.string().optional(),
});

export type BankAccount = z.infer<typeof BankAccountSchema>;
export type CreateBankAccount = z.infer<typeof CreateBankAccountSchema>;
export type UpdateBankAccount = z.infer<typeof UpdateBankAccountSchema>;

// Purchase Tax Account (Umsatzsteuerkonto) schemas
export const PurchaseTaxAccountSchema = z.object({
  id: z.number(),
  name: z.string(),
});

export const CostAccountSchema = z.object({
  id: z.number(),
  costaccount: z.number(),
  name: z.string(),
  section: z.string(),
  group: z.string(),
  inventory: z.boolean(),
  index_incometax: z.array(z.string()),
  deductibility_tax_percent: z.string().nullable(),
  deductibility_amount_percent: z.string().nullable(),
  description: z.string(),
  active: z.boolean(),
  purchasetaxaccounts: z.array(PurchaseTaxAccountSchema),
  counter_booked_bookings: z.number(),
  counter_open_bookings: z.number(),
  counter_deleted_bookings: z.number(),
  counter_bookingtemplates: z.number(),
});

export const CreateCostAccountSchema = z.object({
  costaccount: z.number(),
});

export type CostAccount = z.infer<typeof CostAccountSchema>;
export type CreateCostAccount = z.infer<typeof CreateCostAccountSchema>;

// Purchase Tax Account (Umsatzsteuerkonto) full schemas
export const FullPurchaseTaxAccountSchema = z.object({
  id: z.number(),
  purchasetaxaccount: z.number(),
  name: z.string(),
  section: z.string(),
  group: z.string(),
  reverse_charge: z.boolean(),
  ic_report: z.boolean(),
  ic_delivery: z.boolean(),
  ic_service: z.boolean(),
  ioss_report: z.boolean(),
  eu_oss_report: z.boolean(),
  tax_values: z.array(z.string()),
  index_purchasetax: z.array(z.string()),
  description: z.string(),
  active: z.boolean(),
  counter_booked_bookings: z.number(),
  counter_open_bookings: z.number(),
  counter_deleted_bookings: z.number(),
  counter_bookingtemplates: z.number(),
});

export const CreatePurchaseTaxAccountSchema = z.object({
  purchasetaxaccount: z.number(),
});

export type FullPurchaseTaxAccount = z.infer<typeof FullPurchaseTaxAccountSchema>;
export type CreatePurchaseTaxAccount = z.infer<typeof CreatePurchaseTaxAccountSchema>;

// Cost Center (Kostenstelle) schemas
export const CostCenterSchema = z.object({
  id: z.number(),
  name: z.string(),
  position: z.number(),
  counter_booked_bookings: z.number(),
  counter_open_bookings: z.number(),
  counter_deleted_bookings: z.number(),
  counter_bookingtemplates: z.number(),
});

export const CreateCostCenterSchema = z.object({
  name: z.string().max(40),
  position: z.number().optional(),
});

export const UpdateCostCenterSchema = z.object({
  name: z.string().max(40).optional(),
  position: z.number().optional(),
});

export type CostCenter = z.infer<typeof CostCenterSchema>;
export type CreateCostCenter = z.infer<typeof CreateCostCenterSchema>;
export type UpdateCostCenter = z.infer<typeof UpdateCostCenterSchema>;

// Configuration
export interface BookamatConfig {
  baseUrl: string;
  apiVersion: string;
  country: string;
  year: string;
  username: string;
  apiKey: string;
}

// Error types
export class BookamatApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public response?: any
  ) {
    super(message);
    this.name = "BookamatApiError";
  }
}
