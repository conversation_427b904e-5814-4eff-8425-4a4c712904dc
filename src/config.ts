import { BookamatConfig } from "./types.js";

export function getConfig(): BookamatConfig {
  const baseUrl = process.env.BOOKAMAT_BASE_URL || "https://www.bookamat.com";
  const apiVersion = process.env.BOOKAMAT_API_VERSION || "v1";
  const country = process.env.BOOKAMAT_COUNTRY;
  const year = process.env.BOOKAMAT_YEAR;
  const username = process.env.BOOKAMAT_USERNAME;
  const apiKey = process.env.BOOKAMAT_API_KEY;

  if (!country) {
    throw new Error("BOOKAMAT_COUNTRY environment variable is required (e.g., 'at', 'de')");
  }

  if (!year) {
    throw new Error("BOOKAMAT_YEAR environment variable is required (e.g., '2024')");
  }

  if (!username) {
    throw new Error("BOOKAMAT_USERNAME environment variable is required");
  }

  if (!apiKey) {
    throw new Error("BOOKAMAT_API_KEY environment variable is required");
  }

  return {
    baseUrl,
    apiVersion,
    country,
    year,
    username,
    apiKey,
  };
}

export function validateConfig(config: BookamatConfig): void {
  const requiredFields = ['baseUrl', 'apiVersion', 'country', 'year', 'username', 'apiKey'];
  
  for (const field of requiredFields) {
    if (!config[field as keyof BookamatConfig]) {
      throw new Error(`Configuration field '${field}' is required`);
    }
  }

  // Validate country code
  const validCountries = ['at', 'de', 'ch']; // Austria, Germany, Switzerland
  if (!validCountries.includes(config.country.toLowerCase())) {
    throw new Error(`Invalid country code '${config.country}'. Valid options: ${validCountries.join(', ')}`);
  }

  // Validate year format
  const currentYear = new Date().getFullYear();
  const year = parseInt(config.year);
  if (isNaN(year) || year < 2000 || year > currentYear + 1) {
    throw new Error(`Invalid year '${config.year}'. Must be between 2000 and ${currentYear + 1}`);
  }
}
